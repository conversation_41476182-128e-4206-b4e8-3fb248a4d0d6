{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [

        {
            "name": "Python: Reproduce",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "justMyCode": false,
            "python": "/mnt/miniconda3/envs/m1/bin/python",
            "console": "integratedTerminal",
            "cwd": "${fileDirname}",
            "args": [
                "--method", "step_by_step",
                "--model", "gpt-4o",
                "--azure_endpoint", "https://cloudgpt-openai.azure-api.net/",
                "--api_version", "2025-04-01-preview",
                "--is_handcrafted", "True",
                "--directory_path", "../Who&When_new/Hand-Crafted",
                "--output_dir", "outputs_new/with_gt/5",
                "--prompt_template", "add_step_idx",
                "--with_gt", "True"
            ]
        },
        {
            "name": "Python: Async Debug",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "justMyCode": false,
            "python": "/mnt/miniconda3/envs/m1/bin/python",
            "console": "integratedTerminal",
            "cwd": "${fileDirname}",
            "args": [
                "--scenario", "1",
            ],
            "asyncio": true  // 关键：启用异步调试
        },
        {
            "name": "Run M1 Test (asyncio)",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/scripts/run_m1_test.py",
            "console": "integratedTerminal",
            "justMyCode": false,            // 需要查看库代码
            "python": "/mnt/miniconda3/envs/m1/bin/python",
            "args": [
                "--scenario", "1",
                "--endpoint_type", "cloudgpt",
                "--model", "gpt-4o-mini-20240718",

            ],
            "env": {
              "PYTHONASYNCIODEBUG": "1",   // 打开 asyncio 调试
              "LOG_LEVEL": "DEBUG"
            },
            "subProcess": true,             // 跟踪子进程（如有）
            "redirectOutput": true,                    // 可传入脚本参数
            "cwd": "${workspaceFolder}",
            "justMyCode": true,           // 若需深入 asyncio，可设 true
        },        
        {
            "name": "Python: Jue",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "justMyCode": false,
            "python": "/mnt/miniconda3/envs/ada/bin/python",
            "console": "integratedTerminal",
            "cwd": "${fileDirname}",
            "args": [
                "--scenario", "1",
                "--endpoint_type", "cloudgpt",
                "--model", "gpt-4o-mini-20240718"
            ],
            "asyncio": true  // 关键：启用异步调试
        }
    ]
}