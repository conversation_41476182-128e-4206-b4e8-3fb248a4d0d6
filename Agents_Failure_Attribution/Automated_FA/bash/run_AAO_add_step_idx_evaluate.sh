#!/bin/bash

# 精简版：评估 AAO add_step_idx 多轮数据
echo "🚀 Starting AAO add_step_idx evaluation..."

# 遍历所有目录和文件
for gt_type in with_gt without_gt; do
    echo "=== $gt_type ==="
    for round in {1..4}; do
        dir="../outputs_new/AAO/add_step_idx/$gt_type/$round"
        [ ! -d "$dir" ] && continue

        echo "Round $round:"
        for file in "$dir"/*.txt; do
            [ ! -f "$file" ] && continue
            [[ $(basename "$file") == evaluation_* ]] && continue

            # 根据文件名判断数据路径
            if [[ "$file" == *"handcrafted"* ]]; then
                data_path="../../Who&When/Hand-Crafted"
            else
                data_path="../../Who&When/Algorithm-Generated"
            fi

            echo "  $(basename "$file") -> $data_path"
            python3 ../evaluate_save_outputs.py --eval_file "$file" --data_path "$data_path"
        done
    done
done

echo "✅ Done!"
